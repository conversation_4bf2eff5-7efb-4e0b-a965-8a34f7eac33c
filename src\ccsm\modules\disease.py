"""
疾病自然史模块
实现腺瘤-癌通路和锯齿状腺瘤-癌变通路的完整疾病进展模拟
"""

import numpy as np
import random
from typing import Dict, List, Optional, Tuple
from scipy.stats import norm
from dataclasses import dataclass

from ..core.enums import (
    Gender, CancerStage, AdenomaLocation, DeathCause, RiskFactor
)
from ..core.individual import Individual, Adenoma
from ..data.data_loader import DataLoader


@dataclass
class DiseaseParameters:
    """疾病参数配置"""
    # 腺瘤产生参数（乙状函数）
    adenoma_generation_a: float = 0.1      # 乙状函数参数a
    adenoma_generation_b: float = 50.0     # 乙状函数参数b（中点年龄）
    adenoma_generation_c: float = 0.01     # 乙状函数参数c（最大概率）

    # 腺瘤进展参数（正态分布）
    low_to_high_risk_mean: float = 60.0    # 低风险到高风险进展均值年龄
    low_to_high_risk_std: float = 15.0     # 低风险到高风险进展标准差
    high_to_preclinical_mean: float = 65.0 # 高风险到临床前均值年龄
    high_to_preclinical_std: float = 12.0  # 高风险到临床前标准差

    # 癌症进展参数
    preclinical_to_clinical_prob: float = 0.1  # 临床前到临床概率
    clinical_progression_prob: float = 0.2     # 临床癌症分期进展概率

    # 停留时间参数
    sojourn_time_mean: float = 3.0         # 腺瘤停留时间均值（年）
    sojourn_time_std: float = 0.5          # 腺瘤停留时间标准差
    dwell_time_mean: float = 13.0           # 临床前癌症停留时间均值（年）
    dwell_time_std: float = 0.3            # 临床前癌症停留时间标准差

    # 性别效应
    male_adenoma_multiplier: float = 1.2   # 男性腺瘤产生倍数
    male_progression_multiplier: float = 1.1  # 男性进展倍数

    # 位置分布概率
    proximal_colon_prob: float = 0.4       # 近端结肠概率
    distal_colon_prob: float = 0.4         # 远端结肠概率
    rectum_prob: float = 0.2               # 直肠概率

    # 锯齿状腺瘤参数
    serrated_adenoma_prob: float = 0.15    # 锯齿状腺瘤概率
    small_to_large_serrated_prob: float = 0.1  # 小到大锯齿状腺瘤进展概率

    # 生存参数
    stage_i_5year_survival: float = 0.90   # I期5年生存率
    stage_ii_5year_survival: float = 0.80  # II期5年生存率
    stage_iii_5year_survival: float = 0.65 # III期5年生存率
    stage_iv_5year_survival: float = 0.15  # IV期5年生存率


class DiseaseRiskModule:
    """疾病风险模块"""

    def __init__(self):
        # 风险因素权重（基于文献综述）
        self.risk_factors_weights = {
            RiskFactor.FAMILY_HISTORY: 1.5,              # 一级亲属史
            RiskFactor.INFLAMMATORY_BOWEL_DISEASE: 2.0,  # 炎性肠病史
            RiskFactor.OVERWEIGHT: 1.2,                  # 超重肥胖
            RiskFactor.DIABETES: 1.3,                    # 糖尿病
            RiskFactor.SMOKING: 1.4,                     # 吸烟
            RiskFactor.SEDENTARY_LIFESTYLE: 1.2          # 静坐生活方式
        }

    def calculate_risk_score(self, individual: Individual) -> float:
        """计算个体风险评分"""
        risk_score = 1.0
        for factor, present in individual.risk_factors.items():
            if present and factor in self.risk_factors_weights:
                risk_score *= self.risk_factors_weights[factor]
        return risk_score

    def get_adenoma_generation_probability(self, age: int, gender: Gender,
                                         risk_score: float, params: DiseaseParameters) -> float:
        """计算腺瘤产生概率（乙状函数分布）"""
        # 年龄效应（乙状函数）
        age_effect = params.adenoma_generation_c / (
            1 + np.exp(-params.adenoma_generation_a * (age - params.adenoma_generation_b))
        )

        # 性别效应
        gender_effect = params.male_adenoma_multiplier if gender == Gender.MALE else 1.0

        # 综合概率
        prob = age_effect * gender_effect * risk_score
        return min(prob, 1.0)  # 限制最大概率为1


class DiseaseNaturalHistoryModule:
    """疾病自然史模块"""

    def __init__(self, params: Optional[DiseaseParameters] = None):
        self.params = params or DiseaseParameters()
        self.risk_module = DiseaseRiskModule()
        self.adenoma_id_counter = 0

        # 初始化数据加载器
        self.data_loader = DataLoader()

        # 加载基准数据
        self.benchmark_data = self.data_loader.load_benchmark_data()

        # 根据实际数据调整参数
        self._adjust_parameters_from_data()

    def _adjust_parameters_from_data(self):
        """根据实际数据调整疾病参数"""
        try:
            # 调整腺瘤患病率相关参数
            if 'adenoma_prevalence' in self.benchmark_data:
                adenoma_data = self.benchmark_data['adenoma_prevalence']

                # 计算平均患病率来调整基础概率
                if 'total' in adenoma_data:
                    ages = [age for age in adenoma_data['total'].keys() if 50 <= age <= 70]
                    if ages:
                        avg_prevalence = np.mean([adenoma_data['total'][age] for age in ages])

                        # 根据实际患病率调整腺瘤生成概率
                        adjustment_factor = avg_prevalence / 0.2  # 假设默认患病率为20%
                        self.params.adenoma_generation_a *= adjustment_factor

                        print(f"✅ 根据实际数据调整腺瘤生成概率: 调整因子 {adjustment_factor:.3f}")

            # 调整发病率相关参数
            if 'incidence_rates' in self.benchmark_data:
                incidence_data = self.benchmark_data['incidence_rates']

                if 'total' in incidence_data:
                    ages = [age for age in incidence_data['total'].keys() if 50 <= age <= 70]
                    if ages:
                        avg_incidence = np.mean([incidence_data['total'][age] for age in ages])

                        # 调整癌症进展概率
                        adjustment_factor = avg_incidence / 0.0005  # 假设默认发病率
                        self.params.cancer_progression_prob *= adjustment_factor

                        print(f"✅ 根据实际数据调整癌症进展概率: 调整因子 {adjustment_factor:.3f}")

            # 调整死亡率相关参数
            if 'mortality_rates' in self.benchmark_data:
                mortality_data = self.benchmark_data['mortality_rates']

                if 'total' in mortality_data:
                    ages = [age for age in mortality_data['total'].keys() if 50 <= age <= 70]
                    if ages:
                        avg_mortality = np.mean([mortality_data['total'][age] for age in ages])

                        # 调整癌症死亡概率
                        adjustment_factor = avg_mortality / 0.0002  # 假设默认死亡率
                        # 这里可以调整癌症相关的死亡参数

                        print(f"✅ 根据实际数据调整死亡率参数: 调整因子 {adjustment_factor:.3f}")

        except Exception as e:
            print(f"⚠️ 根据实际数据调整参数失败: {e}")

    def _get_next_adenoma_id(self) -> int:
        """获取下一个腺瘤ID"""
        self.adenoma_id_counter += 1
        return self.adenoma_id_counter

    def _sigmoid_function(self, x: float, a: float, b: float, c: float) -> float:
        """乙状函数"""
        return c / (1 + np.exp(-a * (x - b)))

    def _normal_probability(self, x: float, mean: float, std: float) -> float:
        """正态分布概率密度函数"""
        return norm.pdf(x, mean, std)

    def _generate_adenoma_location(self) -> AdenomaLocation:
        """随机生成腺瘤位置"""
        rand = random.random()
        if rand < self.params.proximal_colon_prob:
            return AdenomaLocation.PROXIMAL_COLON
        elif rand < self.params.proximal_colon_prob + self.params.distal_colon_prob:
            return AdenomaLocation.DISTAL_COLON
        else:
            return AdenomaLocation.RECTUM

    def _generate_adenoma_size(self) -> float:
        """生成腺瘤初始大小（mm）"""
        # 腺瘤初始大小通常较小，使用对数正态分布
        return max(1.0, np.random.lognormal(mean=1.0, sigma=0.5))

    def _should_generate_adenoma(self, individual: Individual, current_year: int) -> bool:
        """判断是否应该产生新腺瘤"""
        age = individual.get_age_at_year(current_year)
        risk_score = self.risk_module.calculate_risk_score(individual)

        # 基础概率
        base_prob = self.risk_module.get_adenoma_generation_probability(
            age, individual.gender, risk_score, self.params
        )

        # 筛查保护效应（适度降低强度）
        if individual.last_screening_year is not None:
            years_since_screening = current_year - individual.last_screening_year
            # 筛查后保护效应：前2年内有轻微保护效应
            if years_since_screening <= 2:
                protection_factor = max(0.85, 1.0 - 0.075 * (2 - years_since_screening))
                base_prob *= protection_factor

        return random.random() < base_prob


    def _create_new_adenoma(self, individual: Individual, current_year: int) -> Adenoma:
        """创建新腺瘤"""
        age = individual.get_age_at_year(current_year)
        location = self._generate_adenoma_location()
        size = self._generate_adenoma_size()

        # 判断是否为锯齿状腺瘤
        is_serrated = random.random() < self.params.serrated_adenoma_prob

        adenoma = Adenoma(
            id=self._get_next_adenoma_id(),
            location=location,
            size=size,
            has_villous=False,
            has_high_grade_dysplasia=False,
            onset_age=age,
            onset_year=current_year
        )

        return adenoma

    def _progress_adenoma(self, adenoma: Adenoma, individual: Individual,
                         current_year: int) -> Optional[CancerStage]:
        """腺瘤进展逻辑"""
        age = individual.get_age_at_year(current_year)

        # 腺瘤大小增长（简化模型）
        years_since_onset = current_year - adenoma.onset_year
        if years_since_onset > 0:
            # 腺瘤大小随时间增长（季度增长）
            growth_rate = 0.5 / 4  # mm/季度
            adenoma.size += growth_rate * years_since_onset * 4  # 转换为季度数

        # 低风险腺瘤到高风险腺瘤的进展
        if not adenoma.is_high_risk:
            # 基于年龄的进展概率（正态分布）
            progression_prob = self._normal_probability(
                age, self.params.low_to_high_risk_mean, self.params.low_to_high_risk_std
            )

            # 性别效应
            if individual.gender == Gender.MALE:
                progression_prob *= self.params.male_progression_multiplier

            # 筛查保护效应（适度降低强度）
            if individual.last_screening_year is not None:
                years_since_screening = current_year - individual.last_screening_year
                if years_since_screening <= 2:
                    protection_factor = max(0.9, 1.0 - 0.05 * (2 - years_since_screening))
                    progression_prob *= protection_factor

            # 标准化概率（转换为季度概率）
            progression_prob *= 0.01 / 4  # 调整为合理的季度概率

            if random.random() < progression_prob:
                # 随机选择高风险特征
                rand = random.random()
                if rand < 0.4:
                    adenoma.size = max(adenoma.size, 10.0)  # 大小≥10mm
                elif rand < 0.7:
                    adenoma.has_villous = True  # 含绒毛
                else:
                    adenoma.has_high_grade_dysplasia = True  # 高级别上皮内瘤变

        # 高风险腺瘤到临床前癌症的进展
        elif adenoma.is_high_risk:
            progression_prob = self._normal_probability(
                age, self.params.high_to_preclinical_mean, self.params.high_to_preclinical_std
            )

            # 性别效应
            if individual.gender == Gender.MALE:
                progression_prob *= self.params.male_progression_multiplier

            # 筛查保护效应（适度降低强度）
            if individual.last_screening_year is not None:
                years_since_screening = current_year - individual.last_screening_year
                if years_since_screening <= 2:
                    protection_factor = max(0.8, 1.0 - 0.1 * (2 - years_since_screening))
                    progression_prob *= protection_factor

            # 标准化概率（转换为季度概率）
            progression_prob *= 0.005 / 4  # 调整为合理的季度概率

            if random.random() < progression_prob:
                return CancerStage.PRECLINICAL_CANCER

        return None

    def _progress_cancer_stage(self, individual: Individual, current_year: int) -> CancerStage:
        """癌症分期进展"""
        current_stage = individual.cancer_stage

        # 临床前癌症到临床癌症I期（季度概率）
        if current_stage == CancerStage.PRECLINICAL_CANCER:
            quarterly_prob = 1 - (1 - self.params.preclinical_to_clinical_prob) ** (1/4)
            if random.random() < quarterly_prob:
                individual.cancer_onset_year = current_year
                return CancerStage.CLINICAL_CANCER_STAGE_I

        # 临床癌症分期进展（季度概率）
        elif current_stage == CancerStage.CLINICAL_CANCER_STAGE_I:
            quarterly_prob = 1 - (1 - self.params.clinical_progression_prob) ** (1/4)
            if random.random() < quarterly_prob:
                return CancerStage.CLINICAL_CANCER_STAGE_II

        elif current_stage == CancerStage.CLINICAL_CANCER_STAGE_II:
            quarterly_prob = 1 - (1 - self.params.clinical_progression_prob) ** (1/4)
            if random.random() < quarterly_prob:
                return CancerStage.CLINICAL_CANCER_STAGE_III

        elif current_stage == CancerStage.CLINICAL_CANCER_STAGE_III:
            quarterly_prob = 1 - (1 - self.params.clinical_progression_prob) ** (1/4)
            if random.random() < quarterly_prob:
                return CancerStage.CLINICAL_CANCER_STAGE_IV

        return current_stage

    def _calculate_cancer_death_probability(self, individual: Individual,
                                          current_year: int) -> float:
        """计算癌症死亡概率"""
        if individual.cancer_onset_year is None:
            return 0.0

        years_since_diagnosis = current_year - individual.cancer_onset_year

        # 结直肠癌导致的死亡仅限于诊断后5年内
        if years_since_diagnosis > 5:
            return 0.0

        # 基于癌症分期的5年生存率计算年度死亡概率
        stage = individual.cancer_stage
        if stage == CancerStage.CLINICAL_CANCER_STAGE_I:
            survival_rate = self.params.stage_i_5year_survival
        elif stage == CancerStage.CLINICAL_CANCER_STAGE_II:
            survival_rate = self.params.stage_ii_5year_survival
        elif stage == CancerStage.CLINICAL_CANCER_STAGE_III:
            survival_rate = self.params.stage_iii_5year_survival
        elif stage == CancerStage.CLINICAL_CANCER_STAGE_IV:
            survival_rate = self.params.stage_iv_5year_survival
        else:
            return 0.0

        # 将5年生存率转换为年度死亡概率
        annual_death_prob = 1 - (survival_rate ** (1/5))
        return annual_death_prob

    def _progress_serrated_pathway(self, individual: Individual, current_year: int) -> CancerStage:
        """锯齿状病变-癌症通路进展"""
        current_stage = individual.cancer_stage

        # 小无蒂锯齿状腺瘤到大无蒂锯齿状腺瘤（季度概率）
        if current_stage == CancerStage.SMALL_SERRATED_ADENOMA:
            quarterly_prob = 1 - (1 - self.params.small_to_large_serrated_prob) ** (1/4)
            if random.random() < quarterly_prob:
                return CancerStage.LARGE_SERRATED_ADENOMA

        # 大无蒂锯齿状腺瘤到临床前癌症
        elif current_stage == CancerStage.LARGE_SERRATED_ADENOMA:
            # 使用与高风险腺瘤相似的进展概率
            age = individual.get_age_at_year(current_year)
            progression_prob = self._normal_probability(
                age, self.params.high_to_preclinical_mean, self.params.high_to_preclinical_std
            )
            progression_prob *= 0.003 / 4  # 锯齿状通路进展稍慢（季度概率）

            if random.random() < progression_prob:
                return CancerStage.PRECLINICAL_CANCER

        return current_stage

    def progress_disease(self, individual: Individual, current_year: int) -> Individual:
        """主要的疾病进展函数"""
        if not individual.alive:
            return individual

        # 1. 腺瘤产生
        if individual.cancer_stage == CancerStage.NORMAL:
            if self._should_generate_adenoma(individual, current_year):
                new_adenoma = self._create_new_adenoma(individual, current_year)
                individual.add_adenoma(new_adenoma)

                # 判断是否直接进入锯齿状通路
                if random.random() < self.params.serrated_adenoma_prob:
                    individual.cancer_stage = CancerStage.SMALL_SERRATED_ADENOMA
                else:
                    individual.cancer_stage = CancerStage.LOW_RISK_ADENOMA

        # 2. 腺瘤进展（传统腺瘤-癌通路）
        elif individual.cancer_stage in [CancerStage.LOW_RISK_ADENOMA, CancerStage.HIGH_RISK_ADENOMA]:
            # 处理现有腺瘤的进展
            for adenoma in individual.adenomas:
                progression_result = self._progress_adenoma(adenoma, individual, current_year)
                if progression_result == CancerStage.PRECLINICAL_CANCER:
                    individual.cancer_stage = CancerStage.PRECLINICAL_CANCER
                    individual.cancer_location = adenoma.location
                    break

            # 更新个体的癌症阶段基于腺瘤状态
            if individual.cancer_stage != CancerStage.PRECLINICAL_CANCER:
                high_risk_adenomas = individual.get_high_risk_adenomas()
                if high_risk_adenomas:
                    individual.cancer_stage = CancerStage.HIGH_RISK_ADENOMA
                elif individual.adenomas:
                    individual.cancer_stage = CancerStage.LOW_RISK_ADENOMA
                else:
                    individual.cancer_stage = CancerStage.NORMAL

        # 3. 锯齿状病变进展
        elif individual.cancer_stage in [CancerStage.SMALL_SERRATED_ADENOMA,
                                       CancerStage.LARGE_SERRATED_ADENOMA]:
            individual.cancer_stage = self._progress_serrated_pathway(individual, current_year)

        # 4. 癌症分期进展
        elif individual.cancer_stage in [CancerStage.PRECLINICAL_CANCER,
                                       CancerStage.CLINICAL_CANCER_STAGE_I,
                                       CancerStage.CLINICAL_CANCER_STAGE_II,
                                       CancerStage.CLINICAL_CANCER_STAGE_III]:
            individual.cancer_stage = self._progress_cancer_stage(individual, current_year)

        # 5. 癌症死亡
        if individual.cancer_stage in [CancerStage.CLINICAL_CANCER_STAGE_I,
                                     CancerStage.CLINICAL_CANCER_STAGE_II,
                                     CancerStage.CLINICAL_CANCER_STAGE_III,
                                     CancerStage.CLINICAL_CANCER_STAGE_IV]:
            death_prob = self._calculate_cancer_death_probability(individual, current_year)
            if random.random() < death_prob:
                individual.set_death(current_year, DeathCause.CANCER)

        return individual

    def get_disease_statistics(self, population: List[Individual]) -> Dict[str, int]:
        """获取疾病统计信息"""
        stats = {
            'normal': 0,
            'low_risk_adenoma': 0,
            'high_risk_adenoma': 0,
            'small_serrated_adenoma': 0,
            'large_serrated_adenoma': 0,
            'preclinical_cancer': 0,
            'clinical_cancer_stage_i': 0,
            'clinical_cancer_stage_ii': 0,
            'clinical_cancer_stage_iii': 0,
            'clinical_cancer_stage_iv': 0,
            'cancer_deaths': 0,
            'total_adenomas': 0
        }

        for individual in population:
            if not individual.alive:
                if individual.death_cause == DeathCause.CANCER:
                    stats['cancer_deaths'] += 1
                continue

            stage = individual.cancer_stage
            if stage == CancerStage.NORMAL:
                stats['normal'] += 1
            elif stage == CancerStage.LOW_RISK_ADENOMA:
                stats['low_risk_adenoma'] += 1
            elif stage == CancerStage.HIGH_RISK_ADENOMA:
                stats['high_risk_adenoma'] += 1
            elif stage == CancerStage.SMALL_SERRATED_ADENOMA:
                stats['small_serrated_adenoma'] += 1
            elif stage == CancerStage.LARGE_SERRATED_ADENOMA:
                stats['large_serrated_adenoma'] += 1
            elif stage == CancerStage.PRECLINICAL_CANCER:
                stats['preclinical_cancer'] += 1
            elif stage == CancerStage.CLINICAL_CANCER_STAGE_I:
                stats['clinical_cancer_stage_i'] += 1
            elif stage == CancerStage.CLINICAL_CANCER_STAGE_II:
                stats['clinical_cancer_stage_ii'] += 1
            elif stage == CancerStage.CLINICAL_CANCER_STAGE_III:
                stats['clinical_cancer_stage_iii'] += 1
            elif stage == CancerStage.CLINICAL_CANCER_STAGE_IV:
                stats['clinical_cancer_stage_iv'] += 1

            stats['total_adenomas'] += individual.adenoma_count

        return stats

    def initialize_individual_risk_factors(self, individual: Individual,
                                         risk_factor_prevalence: Optional[Dict[RiskFactor, float]] = None):
        """初始化个体风险因素"""
        if risk_factor_prevalence is None:
            # 默认风险因素患病率（基于中国人群数据）
            risk_factor_prevalence = {
                RiskFactor.FAMILY_HISTORY: 0.10,              # 10%有家族史
                RiskFactor.INFLAMMATORY_BOWEL_DISEASE: 0.01,  # 1%有炎性肠病史
                RiskFactor.OVERWEIGHT: 0.30,                  # 30%超重肥胖
                RiskFactor.DIABETES: 0.12,                    # 12%糖尿病
                RiskFactor.SMOKING: 0.25,                     # 25%吸烟
                RiskFactor.SEDENTARY_LIFESTYLE: 0.40          # 40%静坐生活方式
            }

        for factor, prevalence in risk_factor_prevalence.items():
            individual.risk_factors[factor] = random.random() < prevalence

        # 更新风险评分
        individual.risk_score = self.risk_module.calculate_risk_score(individual)

    def get_progression_probabilities(self, age: int, gender: Gender) -> Dict[str, float]:
        """获取指定年龄和性别的疾病进展概率"""
        return {
            'adenoma_generation': self.risk_module.get_adenoma_generation_probability(
                age, gender, 1.0, self.params
            ),
            'low_to_high_risk': self._normal_probability(
                age, self.params.low_to_high_risk_mean, self.params.low_to_high_risk_std
            ) * 0.01,
            'high_to_preclinical': self._normal_probability(
                age, self.params.high_to_preclinical_mean, self.params.high_to_preclinical_std
            ) * 0.005,
            'preclinical_to_clinical': self.params.preclinical_to_clinical_prob,
            'clinical_progression': self.params.clinical_progression_prob
        }