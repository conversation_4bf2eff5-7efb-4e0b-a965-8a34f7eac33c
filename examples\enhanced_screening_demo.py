"""
增强筛查功能演示
展示新的筛查策略配置、统计功能和贯序筛查逻辑
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.ccsm.modules.screening import (
    ScreeningModule, ScreeningParameters, ScreeningStrategy, ScreeningToolConfig
)
from src.ccsm.core.individual import Individual
from src.ccsm.core.enums import Gender, ScreeningTool, CancerStage
import pandas as pd


def demo_enhanced_screening_strategies():
    """演示增强的筛查策略配置"""
    print("=== 增强筛查策略配置演示 ===\n")
    
    # 1. 创建自定义的多阶段筛查策略
    print("1. 创建自定义多阶段筛查策略")
    custom_strategy = ScreeningStrategy(
        name="custom_multi_stage",
        tool_configs=[
            ScreeningToolConfig(
                tool=ScreeningTool.RISK_QUESTIONNAIRE,
                start_age=45,
                end_age=50,
                interval=5.0,
                compliance_rate=0.85
            ),
            ScreeningToolConfig(
                tool=ScreeningTool.FIT,
                start_age=50,
                end_age=65,
                interval=2.0,
                compliance_rate=0.75,
                follow_up_compliance_rate=0.80
            ),
            ScreeningToolConfig(
                tool=ScreeningTool.COLONOSCOPY,
                start_age=65,
                end_age=75,
                interval=10.0,
                compliance_rate=0.65
            )
        ],
        sequential=True
    )
    
    print(f"策略名称: {custom_strategy.name}")
    print(f"工具数量: {len(custom_strategy.tool_configs)}")
    for i, config in enumerate(custom_strategy.tool_configs):
        print(f"  工具 {i+1}: {config.tool.value}")
        print(f"    年龄范围: {config.start_age}-{config.end_age}岁")
        print(f"    筛查间隔: {config.interval}年")
        print(f"    筛查依从性: {config.compliance_rate}")
        if config.follow_up_compliance_rate:
            print(f"    后续肠镜依从性: {config.follow_up_compliance_rate}")
    print()


def demo_screening_statistics():
    """演示增强的筛查统计功能"""
    print("=== 增强筛查统计功能演示 ===\n")
    
    # 创建筛查模块
    screening_module = ScreeningModule()
    screening_module.create_predefined_strategies()
    
    # 创建测试人群
    population = []
    for i in range(100):
        individual = Individual(
            id=i,
            gender=Gender.MALE if i % 2 == 0 else Gender.FEMALE,
            birth_year=1970 + (i % 20),  # 年龄分布在50-70岁
            current_age=54 + (i % 20)
        )
        # 随机设置一些个体有病变
        if i % 10 == 0:
            individual.cancer_stage = CancerStage.HIGH_RISK_ADENOMA
        elif i % 15 == 0:
            individual.cancer_stage = CancerStage.LOW_RISK_ADENOMA
        
        population.append(individual)
    
    print(f"创建测试人群: {len(population)} 个个体")
    
    # 测试不同策略的统计结果
    strategies_to_test = ["annual_fit", "biennial_fit", "fit_colonoscopy"]
    
    results = {}
    for strategy_name in strategies_to_test:
        print(f"\n测试策略: {strategy_name}")
        
        # 重置人群状态
        for individual in population:
            individual.screening_history.clear()
            individual.last_screening_year = None
        
        # 执行筛查
        stats = screening_module.screen_population(population, strategy_name, 2024)
        results[strategy_name] = stats
        
        print(f"  符合条件人群: {stats['eligible_population']}")
        print(f"  实际筛查人群: {stats['screened_population']}")
        print(f"  筛查覆盖率: {stats['screened_population']/stats['eligible_population']*100:.1f}%")
        print(f"  检出病例数: {stats['detected_cases']}")
        print(f"  真阳性数: {stats['true_positives']}")
        print(f"  假阳性数: {stats['false_positives']}")
        print(f"  初筛次数: {stats['primary_screening_count']}")
        print(f"  确诊性肠镜次数: {stats['diagnostic_colonoscopy_count']}")
        print(f"  后续依从率: {stats['follow_up_compliance_rate']*100:.1f}%")
        print(f"  总成本: ¥{stats['total_cost']:,.0f}")
        
        print("  各工具筛查次数:")
        for tool, count in stats['screening_counts_by_tool'].items():
            if count > 0:
                print(f"    {tool}: {count}次")
    
    return results


def demo_sequential_screening():
    """演示贯序筛查逻辑"""
    print("\n=== 贯序筛查逻辑演示 ===\n")
    
    # 创建筛查模块
    screening_module = ScreeningModule()
    screening_module.create_predefined_strategies()
    
    # 创建有病变的测试个体
    individual = Individual(
        id=1,
        gender=Gender.MALE,
        birth_year=1970,
        current_age=54
    )
    individual.cancer_stage = CancerStage.HIGH_RISK_ADENOMA
    
    print(f"测试个体: {individual.id}号，{individual.current_age}岁男性，有高风险腺瘤")
    
    # 测试FIT+结肠镜贯序筛查
    print("\n执行FIT+结肠镜贯序筛查:")
    records = screening_module.perform_screening(individual, "fit_colonoscopy", 2024)
    
    print(f"筛查记录数量: {len(records)}")
    total_cost = 0
    for i, record in enumerate(records):
        print(f"\n记录 {i+1}:")
        print(f"  工具: {record.tool}")
        print(f"  年龄: {record.age}岁")
        print(f"  依从: {'是' if record.compliant else '否'}")
        print(f"  检测到异常: {'是' if record.detected else '否'}")
        print(f"  真阳性: {'是' if record.true_positive else '否'}")
        print(f"  初筛: {'是' if record.is_primary_screening else '否'}")
        print(f"  确诊性肠镜: {'是' if record.is_diagnostic_colonoscopy else '否'}")
        if record.triggered_by_tool:
            print(f"  触发工具: {record.triggered_by_tool}")
        print(f"  成本: ¥{record.cost:,.0f}")
        total_cost += record.cost
    
    print(f"\n总筛查成本: ¥{total_cost:,.0f}")
    print(f"治疗记录数: {len(individual.treatment_history)}")


def demo_comparison_analysis():
    """演示策略比较分析"""
    print("\n=== 策略比较分析演示 ===\n")
    
    # 创建筛查模块
    screening_module = ScreeningModule()
    screening_module.create_predefined_strategies()
    
    # 创建较大的测试人群
    population = []
    for i in range(1000):
        individual = Individual(
            id=i,
            gender=Gender.MALE if i % 2 == 0 else Gender.FEMALE,
            birth_year=1965 + (i % 25),  # 年龄分布在50-75岁
            current_age=59 + (i % 25)
        )
        # 设置病变状态
        if i % 20 == 0:
            individual.cancer_stage = CancerStage.HIGH_RISK_ADENOMA
        elif i % 30 == 0:
            individual.cancer_stage = CancerStage.LOW_RISK_ADENOMA
        elif i % 100 == 0:
            individual.cancer_stage = CancerStage.PRECLINICAL_CANCER
        
        population.append(individual)
    
    print(f"创建测试人群: {len(population)} 个个体")
    
    # 比较不同策略
    strategies = ["annual_fit", "biennial_fit", "colonoscopy_10y", "fit_colonoscopy"]
    comparison_data = []
    
    for strategy_name in strategies:
        print(f"\n分析策略: {strategy_name}")
        
        # 重置人群状态
        for individual in population:
            individual.screening_history.clear()
            individual.last_screening_year = None
            individual.treatment_history.clear()
        
        # 执行筛查
        stats = screening_module.screen_population(population, strategy_name, 2024)
        
        # 计算关键指标
        screening_rate = stats['screened_population'] / stats['eligible_population'] if stats['eligible_population'] > 0 else 0
        detection_rate = stats['detected_cases'] / stats['screened_population'] if stats['screened_population'] > 0 else 0
        ppv = stats['true_positives'] / stats['detected_cases'] if stats['detected_cases'] > 0 else 0
        cost_per_case = stats['total_cost'] / stats['detected_cases'] if stats['detected_cases'] > 0 else 0
        
        comparison_data.append({
            '策略': strategy_name,
            '符合条件人群': stats['eligible_population'],
            '筛查人群': stats['screened_population'],
            '筛查率(%)': f"{screening_rate*100:.1f}",
            '检出病例': stats['detected_cases'],
            '检出率(%)': f"{detection_rate*100:.1f}",
            '阳性预测值(%)': f"{ppv*100:.1f}",
            '初筛次数': stats['primary_screening_count'],
            '确诊肠镜次数': stats['diagnostic_colonoscopy_count'],
            '总成本(万元)': f"{stats['total_cost']/10000:.1f}",
            '每例检出成本(元)': f"{cost_per_case:.0f}" if cost_per_case > 0 else "N/A"
        })
    
    # 创建比较表格
    df = pd.DataFrame(comparison_data)
    print("\n策略比较结果:")
    print(df.to_string(index=False))


def main():
    """主函数"""
    print("增强筛查功能演示\n")
    print("=" * 60)
    
    try:
        demo_enhanced_screening_strategies()
        results = demo_screening_statistics()
        demo_sequential_screening()
        demo_comparison_analysis()
        
        print("\n" + "=" * 60)
        print("演示完成！")
        
    except Exception as e:
        print(f"演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
