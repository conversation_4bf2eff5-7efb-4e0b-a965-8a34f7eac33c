"""
个体数据结构定义
包含个体的基本信息、疾病状态、筛查历史等
"""

from dataclasses import dataclass, field
from typing import List, Dict, Optional, Any
from .enums import Gender, CancerStage, AdenomaLocation, DeathCause, RiskFactor


@dataclass
class Adenoma:
    """腺瘤数据结构"""
    id: int                                    # 腺瘤ID
    location: AdenomaLocation                  # 腺瘤位置
    size: float                               # 腺瘤大小(mm)
    has_villous: bool = False                 # 是否含绒毛
    has_high_grade_dysplasia: bool = False    # 是否高级别上皮内瘤变
    onset_age: int = 0                        # 发生年龄
    onset_year: int = 0                       # 发生年份

    @property
    def is_high_risk(self) -> bool:
        """判断是否为高风险腺瘤"""
        return (self.size >= 10.0 or
                self.has_villous or
                self.has_high_grade_dysplasia)


@dataclass
class ScreeningRecord:
    """筛查记录数据结构"""
    year: int                                 # 筛查年份
    age: int                                  # 筛查时年龄
    tool: str                                 # 筛查工具
    compliant: bool                           # 是否依从
    detected: bool                            # 是否检测到异常
    true_positive: bool = False               # 真阳性
    false_positive: bool = False              # 假阳性
    false_negative: bool = False              # 假阴性
    follow_up_compliant: bool = False         # 后续依从性
    cost: float = 0.0                        # 筛查成本

    # 新增字段用于详细统计
    is_primary_screening: bool = True         # 是否为初筛（非确诊性肠镜）
    is_diagnostic_colonoscopy: bool = False   # 是否为确诊性肠镜
    triggered_by_tool: Optional[str] = None   # 触发确诊性肠镜的工具
    screening_round: int = 1                  # 筛查轮次
    strategy_name: str = ""                   # 所属策略名称


@dataclass
class TreatmentRecord:
    """治疗记录数据结构"""
    year: int                                 # 治疗年份
    age: int                                  # 治疗时年龄
    treatment_type: str                       # 治疗类型
    cancer_stage: CancerStage                 # 癌症分期
    cost: float = 0.0                        # 治疗成本
    success: bool = True                      # 治疗成功


@dataclass
class Individual:
    """个体数据结构"""
    id: int                                   # 个体ID
    gender: Gender                            # 性别
    birth_year: int                           # 出生年份
    current_age: int                          # 当前年龄
    alive: bool = True                        # 存活状态
    cancer_stage: CancerStage = CancerStage.NORMAL  # 癌症阶段
    death_cause: DeathCause = DeathCause.ALIVE      # 死亡原因
    death_year: Optional[int] = None          # 死亡年份

    # 腺瘤相关
    adenomas: List[Adenoma] = field(default_factory=list)  # 腺瘤列表
    adenoma_count: int = 0                    # 腺瘤数量

    # 风险因素
    risk_factors: Dict[RiskFactor, bool] = field(default_factory=dict)  # 风险因素
    risk_score: float = 1.0                   # 综合风险评分

    # 筛查和治疗历史
    screening_history: List[ScreeningRecord] = field(default_factory=list)  # 筛查历史
    treatment_history: List[TreatmentRecord] = field(default_factory=list)  # 治疗历史
    last_screening_year: Optional[int] = None  # 最后筛查年份

    # 疾病进展相关
    cancer_onset_year: Optional[int] = None    # 癌症发生年份
    cancer_location: Optional[AdenomaLocation] = None  # 癌症位置
    preclinical_duration: float = 0.0         # 临床前期持续时间

    # 经济学相关
    total_screening_cost: float = 0.0         # 总筛查成本
    total_treatment_cost: float = 0.0         # 总治疗成本
    life_years_saved: float = 0.0             # 挽救生命年

    def __post_init__(self):
        """初始化后处理"""
        # 初始化风险因素字典
        if not self.risk_factors:
            self.risk_factors = {factor: False for factor in RiskFactor}

    @property
    def age_at_year(self) -> int:
        """计算指定年份的年龄"""
        return self.current_age

    def get_age_at_year(self, year: int) -> int:
        """计算指定年份的年龄"""
        return year - self.birth_year

    def add_adenoma(self, adenoma: Adenoma) -> None:
        """添加腺瘤"""
        self.adenomas.append(adenoma)
        self.adenoma_count = len(self.adenomas)

    def remove_adenoma(self, adenoma_id: int) -> bool:
        """移除腺瘤（通过筛查发现并切除）"""
        for i, adenoma in enumerate(self.adenomas):
            if adenoma.id == adenoma_id:
                self.adenomas.pop(i)
                self.adenoma_count = len(self.adenomas)
                return True
        return False

    def get_high_risk_adenomas(self) -> List[Adenoma]:
        """获取高风险腺瘤列表"""
        return [adenoma for adenoma in self.adenomas if adenoma.is_high_risk]

    def get_low_risk_adenomas(self) -> List[Adenoma]:
        """获取低风险腺瘤列表"""
        return [adenoma for adenoma in self.adenomas if not adenoma.is_high_risk]

    def add_screening_record(self, record: ScreeningRecord) -> None:
        """添加筛查记录"""
        self.screening_history.append(record)
        self.last_screening_year = record.year
        self.total_screening_cost += record.cost

    def add_treatment_record(self, record: TreatmentRecord) -> None:
        """添加治疗记录"""
        self.treatment_history.append(record)
        self.total_treatment_cost += record.cost

    def set_death(self, year: int, cause: DeathCause) -> None:
        """设置死亡状态"""
        self.alive = False
        self.death_year = year
        self.death_cause = cause

    def is_eligible_for_screening(self, year: int, start_age: int, end_age: int) -> bool:
        """判断是否符合筛查条件"""
        if not self.alive:
            return False

        # 判断年龄是否符合筛查条件
        age = self.get_age_at_year(year)
        return start_age <= age <= end_age

    def get_screening_interval_since_last(self, current_year: int) -> int:
        """获取距离上次筛查的间隔年数"""
        if self.last_screening_year is None:
            return float('inf')
        return current_year - self.last_screening_year

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'id': self.id,
            'gender': self.gender.value,
            'birth_year': self.birth_year,
            'current_age': self.current_age,
            'alive': self.alive,
            'cancer_stage': self.cancer_stage.value,
            'death_cause': self.death_cause.value,
            'death_year': self.death_year,
            'adenoma_count': self.adenoma_count,
            'risk_score': self.risk_score,
            'total_screening_cost': self.total_screening_cost,
            'total_treatment_cost': self.total_treatment_cost,
            'life_years_saved': self.life_years_saved
        }
