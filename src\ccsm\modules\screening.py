"""
筛查模块
实现多种筛查工具和策略的组合实施，支持贯序筛查
"""

import random
from typing import Dict, List, Optional, Tuple, Union
from dataclasses import dataclass, field
from enum import Enum

from ..core.enums import ScreeningTool, CancerStage, AdenomaLocation
from ..core.individual import Individual, ScreeningRecord, TreatmentRecord


@dataclass
class ScreeningParameters:
    """筛查参数配置"""
    # 各种筛查工具的敏感性（针对不同疾病阶段）
    sensitivity: Dict[ScreeningTool, Dict[CancerStage, float]] = field(default_factory=lambda: {
        ScreeningTool.FIT: {
            CancerStage.LOW_RISK_ADENOMA: 0.15,
            CancerStage.HIGH_RISK_ADENOMA: 0.25,
            CancerStage.SMALL_SERRATED_ADENOMA: 0.10,
            CancerStage.LARGE_SERRATED_ADENOMA: 0.20,
            CancerStage.PRECLINICAL_CANCER: 0.70,
            CancerStage.CLINICAL_CANCER_STAGE_I: 0.85,
            CancerStage.CLINICAL_CANCER_STAGE_II: 0.90,
            CancerStage.CLINICAL_CANCER_STAGE_III: 0.90,
            CancerStage.CLINICAL_CANCER_STAGE_IV: 0.90
        },
        ScreeningTool.COLONOSCOPY: {
            CancerStage.LOW_RISK_ADENOMA: 0.95,
            CancerStage.HIGH_RISK_ADENOMA: 0.95,
            CancerStage.SMALL_SERRATED_ADENOMA: 0.90,
            CancerStage.LARGE_SERRATED_ADENOMA: 0.95,
            CancerStage.PRECLINICAL_CANCER: 0.95,
            CancerStage.CLINICAL_CANCER_STAGE_I: 0.95,
            CancerStage.CLINICAL_CANCER_STAGE_II: 0.95,
            CancerStage.CLINICAL_CANCER_STAGE_III: 0.95,
            CancerStage.CLINICAL_CANCER_STAGE_IV: 0.95
        },
        ScreeningTool.SIGMOIDOSCOPY: {
            CancerStage.LOW_RISK_ADENOMA: 0.70,  # 仅能检测远端结肠和直肠
            CancerStage.HIGH_RISK_ADENOMA: 0.75,
            CancerStage.SMALL_SERRATED_ADENOMA: 0.65,
            CancerStage.LARGE_SERRATED_ADENOMA: 0.70,
            CancerStage.PRECLINICAL_CANCER: 0.80,
            CancerStage.CLINICAL_CANCER_STAGE_I: 0.85,
            CancerStage.CLINICAL_CANCER_STAGE_II: 0.85,
            CancerStage.CLINICAL_CANCER_STAGE_III: 0.85,
            CancerStage.CLINICAL_CANCER_STAGE_IV: 0.85
        },
        ScreeningTool.RISK_QUESTIONNAIRE: {
            # 风险问卷主要用于风险分层，不直接检测病变
            CancerStage.LOW_RISK_ADENOMA: 0.0,
            CancerStage.HIGH_RISK_ADENOMA: 0.0,
            CancerStage.SMALL_SERRATED_ADENOMA: 0.0,
            CancerStage.LARGE_SERRATED_ADENOMA: 0.0,
            CancerStage.PRECLINICAL_CANCER: 0.0,
            CancerStage.CLINICAL_CANCER_STAGE_I: 0.0,
            CancerStage.CLINICAL_CANCER_STAGE_II: 0.0,
            CancerStage.CLINICAL_CANCER_STAGE_III: 0.0,
            CancerStage.CLINICAL_CANCER_STAGE_IV: 0.0
        }
    })

    # 特异性
    specificity: Dict[ScreeningTool, float] = field(default_factory=lambda: {
        ScreeningTool.FIT: 0.95,
        ScreeningTool.COLONOSCOPY: 0.98,
        ScreeningTool.SIGMOIDOSCOPY: 0.96,
        ScreeningTool.RISK_QUESTIONNAIRE: 0.70  # 风险问卷的特异性较低
    })

    # 依从性
    compliance: Dict[ScreeningTool, float] = field(default_factory=lambda: {
        ScreeningTool.FIT: 0.70,
        ScreeningTool.COLONOSCOPY: 0.60,
        ScreeningTool.SIGMOIDOSCOPY: 0.65,
        ScreeningTool.RISK_QUESTIONNAIRE: 0.80
    })

    # 阳性后接受肠镜检查的依从性
    follow_up_compliance: Dict[ScreeningTool, float] = field(default_factory=lambda: {
        ScreeningTool.FIT: 0.75,
        ScreeningTool.SIGMOIDOSCOPY: 0.80,
        ScreeningTool.RISK_QUESTIONNAIRE: 0.60
    })

    # 筛查成本
    costs: Dict[ScreeningTool, float] = field(default_factory=lambda: {
        ScreeningTool.FIT: 50.0,
        ScreeningTool.COLONOSCOPY: 1000.0,
        ScreeningTool.SIGMOIDOSCOPY: 300.0,
        ScreeningTool.RISK_QUESTIONNAIRE: 20.0
    })


@dataclass
class ScreeningStrategy:
    """筛查策略配置"""
    name: str                                    # 策略名称
    start_age: int                              # 开始年龄
    end_age: int                                # 结束年龄
    tools: List[ScreeningTool]                  # 筛查工具序列
    intervals: List[int]                        # 筛查间隔（年）
    sequential: bool = True                     # 是否贯序实施
    risk_stratified: bool = False               # 是否基于风险分层
    high_risk_interval: Optional[int] = None    # 高风险人群筛查间隔
    compliance_rates: Optional[Dict[ScreeningTool, float]] = None  # 自定义依从性

    def __post_init__(self):
        """初始化后处理"""
        if len(self.intervals) == 1 and len(self.tools) > 1:
            # 如果只指定了一个间隔，应用到所有工具
            self.intervals = self.intervals * len(self.tools)
        elif len(self.intervals) != len(self.tools):
            raise ValueError("筛查工具数量与间隔数量不匹配")


class ScreeningModule:
    """筛查模块"""

    def __init__(self, params: Optional[ScreeningParameters] = None):
        self.params = params or ScreeningParameters()
        self.strategies: Dict[str, ScreeningStrategy] = {}
        self.screening_history: List[Dict] = []

    def add_strategy(self, strategy: ScreeningStrategy):
        """添加筛查策略"""
        self.strategies[strategy.name] = strategy

    def get_strategy(self, name: str) -> Optional[ScreeningStrategy]:
        """获取筛查策略"""
        return self.strategies.get(name)

    def _calculate_sensitivity(self, tool: ScreeningTool, individual: Individual) -> float:
        """计算筛查工具对个体的敏感性"""
        base_sensitivity = self.params.sensitivity[tool].get(individual.cancer_stage, 0.0)

        # 乙状结直肠镜只能检测远端结肠和直肠的病变
        if tool == ScreeningTool.SIGMOIDOSCOPY:
            if individual.cancer_location == AdenomaLocation.PROXIMAL_COLON:
                return 0.0  # 无法检测近端结肠病变
            elif individual.cancer_location in [AdenomaLocation.DISTAL_COLON, AdenomaLocation.RECTUM]:
                return base_sensitivity

        return base_sensitivity

    def _perform_single_screening(self, individual: Individual, tool: ScreeningTool,
                                current_year: int, strategy: ScreeningStrategy) -> ScreeningRecord:
        """执行单次筛查"""
        age = individual.get_age_at_year(current_year)

        # 获取依从性
        compliance_rate = (strategy.compliance_rates.get(tool) if strategy.compliance_rates
                          else self.params.compliance[tool])

        # 创建筛查记录
        record = ScreeningRecord(
            year=current_year,
            age=age,
            tool=tool.value,
            compliant=random.random() < compliance_rate,
            detected=False,
            cost=self.params.costs[tool]
        )

        if not record.compliant:
            return record

        # 计算检测概率
        sensitivity = self._calculate_sensitivity(tool, individual)
        specificity = self.params.specificity[tool]

        # 判断是否检测到异常
        if individual.cancer_stage != CancerStage.NORMAL:
            # 有病变的情况
            if random.random() < sensitivity:
                record.detected = True
                record.true_positive = True

                # 如果检测到异常且不是肠镜检查，需要后续肠镜确诊
                if tool != ScreeningTool.COLONOSCOPY:
                    follow_up_compliance = self.params.follow_up_compliance.get(tool, 0.0)
                    record.follow_up_compliant = random.random() < follow_up_compliance

                    if record.follow_up_compliant:
                        # 执行后续肠镜检查并移除病变
                        self._perform_treatment(individual, current_year)
            else:
                record.false_negative = True
        else:
            # 无病变的情况
            if random.random() < (1 - specificity):
                record.detected = True
                record.false_positive = True

                # 假阳性也需要后续肠镜检查
                if tool != ScreeningTool.COLONOSCOPY:
                    follow_up_compliance = self.params.follow_up_compliance.get(tool, 0.0)
                    record.follow_up_compliant = random.random() < follow_up_compliance

        return record


    def _perform_treatment(self, individual: Individual, current_year: int):
        """执行治疗（移除腺瘤或癌前病变）"""
        age = individual.get_age_at_year(current_year)

        # 根据疾病阶段确定治疗类型和成本
        treatment_type = "adenoma_removal"
        cost = 2000.0  # 基础治疗成本

        if individual.cancer_stage in [CancerStage.LOW_RISK_ADENOMA, CancerStage.HIGH_RISK_ADENOMA]:
            # 移除腺瘤
            individual.adenomas.clear()  # 简化处理，移除所有腺瘤
            individual.adenoma_count = 0
            individual.cancer_stage = CancerStage.NORMAL
            treatment_type = "adenoma_removal"
            cost = 2000.0

        elif individual.cancer_stage in [CancerStage.SMALL_SERRATED_ADENOMA,
                                       CancerStage.LARGE_SERRATED_ADENOMA]:
            # 移除锯齿状腺瘤
            individual.cancer_stage = CancerStage.NORMAL
            treatment_type = "serrated_adenoma_removal"
            cost = 2500.0

        elif individual.cancer_stage == CancerStage.PRECLINICAL_CANCER:
            # 移除临床前癌症
            individual.cancer_stage = CancerStage.NORMAL
            individual.cancer_location = None
            treatment_type = "preclinical_cancer_removal"
            cost = 5000.0

        elif individual.cancer_stage in [CancerStage.CLINICAL_CANCER_STAGE_I,
                                       CancerStage.CLINICAL_CANCER_STAGE_II,
                                       CancerStage.CLINICAL_CANCER_STAGE_III,
                                       CancerStage.CLINICAL_CANCER_STAGE_IV]:
            # 临床癌症治疗
            treatment_type = f"cancer_treatment_stage_{individual.cancer_stage.value - 5}"
            cost = 20000.0 + (individual.cancer_stage.value - 6) * 15000.0  # 分期越高成本越高

        # 创建治疗记录
        treatment_record = TreatmentRecord(
            year=current_year,
            age=age,
            treatment_type=treatment_type,
            cancer_stage=individual.cancer_stage,
            cost=cost,
            success=True
        )

        individual.add_treatment_record(treatment_record)

    def _should_screen_individual(self, individual: Individual, strategy: ScreeningStrategy,
                                current_year: int, tool_index: int = 0) -> bool:
        """判断个体是否应该接受筛查"""
        age = individual.get_age_at_year(current_year)

        # 检查年龄范围
        if not (strategy.start_age <= age <= strategy.end_age):
            return False

        # 检查筛查间隔
        if individual.last_screening_year is not None:
            interval = strategy.intervals[tool_index] if tool_index < len(strategy.intervals) else strategy.intervals[0]
            if current_year - individual.last_screening_year < interval:
                return False

        # 风险分层筛查
        if strategy.risk_stratified and strategy.high_risk_interval is not None:
            if individual.risk_score > 2.0:  # 高风险阈值
                interval = strategy.high_risk_interval
                if individual.last_screening_year is not None:
                    if current_year - individual.last_screening_year < interval:
                        return False

        return True

    def _perform_risk_assessment(self, individual: Individual) -> float:
        """执行风险评估"""
        # 基于个体风险因素计算风险评分
        risk_score = individual.risk_score

        # 风险问卷的结果影响后续筛查决策
        if risk_score > 2.0:
            return 1.0  # 高风险，建议进一步筛查
        elif risk_score > 1.5:
            return 0.7  # 中等风险
        else:
            return 0.3  # 低风险

    def perform_screening(self, individual: Individual, strategy_name: str,
                         current_year: int) -> List[ScreeningRecord]:
        """为个体执行筛查"""
        strategy = self.strategies.get(strategy_name)
        if not strategy:
            raise ValueError(f"未找到筛查策略: {strategy_name}")

        records = []

        if strategy.sequential:
            # 贯序筛查实施
            records = self._perform_sequential_screening(individual, strategy, current_year)
        else:
            # 单一工具筛查
            if strategy.tools:
                tool = strategy.tools[0]
                if self._should_screen_individual(individual, strategy, current_year):
                    record = self._perform_single_screening(individual, tool, current_year, strategy)
                    records.append(record)
                    individual.add_screening_record(record)

        return records

    def _perform_sequential_screening(self, individual: Individual, strategy: ScreeningStrategy,
                                    current_year: int) -> List[ScreeningRecord]:
        """执行贯序筛查"""
        records = []
        continue_screening = True

        for i, tool in enumerate(strategy.tools):
            if not continue_screening:
                break

            if not self._should_screen_individual(individual, strategy, current_year, i):
                continue

            record = self._perform_single_screening(individual, tool, current_year, strategy)
            records.append(record)
            individual.add_screening_record(record)

            # 贯序筛查逻辑
            if tool == ScreeningTool.RISK_QUESTIONNAIRE:
                # 风险问卷结果决定是否继续
                risk_result = self._perform_risk_assessment(individual)
                if risk_result < 0.5:  # 低风险，不继续筛查
                    continue_screening = False

            elif tool == ScreeningTool.FIT:
                # FIT阳性才继续肠镜检查
                if not (record.detected and record.follow_up_compliant):
                    continue_screening = False

            elif tool == ScreeningTool.SIGMOIDOSCOPY:
                # 乙状结直肠镜阳性才继续全结肠镜检查
                if not (record.detected and record.follow_up_compliant):
                    continue_screening = False

            elif tool == ScreeningTool.COLONOSCOPY:
                # 肠镜检查是最终检查，直接处理结果
                if record.detected and record.true_positive:
                    self._perform_treatment(individual, current_year)
                continue_screening = False

        return records

    def screen_population(self, population: List[Individual], strategy_name: str,
                         current_year: int) -> Dict[str, int]:
        """为人群执行筛查"""
        strategy = self.strategies.get(strategy_name)
        if not strategy:
            raise ValueError(f"未找到筛查策略: {strategy_name}")

        screening_stats = {
            'eligible_population': 0,
            'screened_population': 0,
            'detected_cases': 0,
            'true_positives': 0,
            'false_positives': 0,
            'false_negatives': 0,
            'treatments_performed': 0,
            'total_cost': 0.0
        }

        for individual in population:
            if not individual.alive:
                continue

            age = individual.get_age_at_year(current_year)
            if strategy.start_age <= age <= strategy.end_age:
                screening_stats['eligible_population'] += 1

                records = self.perform_screening(individual, strategy_name, current_year)

                if records:
                    screening_stats['screened_population'] += 1

                    for record in records:
                        screening_stats['total_cost'] += record.cost

                        if record.detected:
                            screening_stats['detected_cases'] += 1

                        if record.true_positive:
                            screening_stats['true_positives'] += 1

                        if record.false_positive:
                            screening_stats['false_positives'] += 1

                        if record.false_negative:
                            screening_stats['false_negatives'] += 1

                    # 统计治疗数量
                    treatments_this_year = len([t for t in individual.treatment_history
                                              if t.year == current_year])
                    screening_stats['treatments_performed'] += treatments_this_year

        # 记录筛查历史
        self.screening_history.append({
            'year': current_year,
            'strategy': strategy_name,
            'stats': screening_stats
        })

        return screening_stats

    def get_screening_statistics(self) -> Dict:
        """获取筛查统计信息"""
        if not self.screening_history:
            return {}

        total_stats = {
            'total_eligible': 0,
            'total_screened': 0,
            'total_detected': 0,
            'total_true_positives': 0,
            'total_false_positives': 0,
            'total_false_negatives': 0,
            'total_treatments': 0,
            'total_cost': 0.0,
            'screening_coverage': 0.0,
            'positive_predictive_value': 0.0,
            'sensitivity': 0.0,
            'specificity': 0.0
        }

        for record in self.screening_history:
            stats = record['stats']
            total_stats['total_eligible'] += stats['eligible_population']
            total_stats['total_screened'] += stats['screened_population']
            total_stats['total_detected'] += stats['detected_cases']
            total_stats['total_true_positives'] += stats['true_positives']
            total_stats['total_false_positives'] += stats['false_positives']
            total_stats['total_false_negatives'] += stats['false_negatives']
            total_stats['total_treatments'] += stats['treatments_performed']
            total_stats['total_cost'] += stats['total_cost']

        # 计算性能指标
        if total_stats['total_eligible'] > 0:
            total_stats['screening_coverage'] = total_stats['total_screened'] / total_stats['total_eligible']

        if total_stats['total_detected'] > 0:
            total_stats['positive_predictive_value'] = total_stats['total_true_positives'] / total_stats['total_detected']

        if (total_stats['total_true_positives'] + total_stats['total_false_negatives']) > 0:
            total_stats['sensitivity'] = total_stats['total_true_positives'] / (
                total_stats['total_true_positives'] + total_stats['total_false_negatives']
            )

        # 特异性计算需要真阴性数据，这里简化处理
        total_stats['specificity'] = 0.95  # 假设平均特异性

        return total_stats

    def create_predefined_strategies(self):
        """创建预定义的筛查策略"""

        # 年度FIT筛查策略
        annual_fit = ScreeningStrategy(
            name="annual_fit",
            start_age=50,
            end_age=75,
            tools=[ScreeningTool.FIT],
            intervals=[1],
            sequential=False
        )
        self.add_strategy(annual_fit)

        # 双年度FIT筛查策略
        biennial_fit = ScreeningStrategy(
            name="biennial_fit",
            start_age=50,
            end_age=75,
            tools=[ScreeningTool.FIT],
            intervals=[2],
            sequential=False
        )
        self.add_strategy(biennial_fit)

        # 10年结肠镜筛查策略
        colonoscopy_10y = ScreeningStrategy(
            name="colonoscopy_10y",
            start_age=50,
            end_age=75,
            tools=[ScreeningTool.COLONOSCOPY],
            intervals=[10],
            sequential=False
        )
        self.add_strategy(colonoscopy_10y)

        # 风险分层筛查策略
        risk_stratified = ScreeningStrategy(
            name="risk_stratified",
            start_age=50,
            end_age=75,
            tools=[ScreeningTool.RISK_QUESTIONNAIRE, ScreeningTool.FIT, ScreeningTool.COLONOSCOPY],
            intervals=[5, 1, 10],
            sequential=True,
            risk_stratified=True,
            high_risk_interval=1
        )
        self.add_strategy(risk_stratified)

        # FIT+结肠镜贯序筛查策略
        fit_colonoscopy = ScreeningStrategy(
            name="fit_colonoscopy",
            start_age=50,
            end_age=75,
            tools=[ScreeningTool.FIT, ScreeningTool.COLONOSCOPY],
            intervals=[1, 10],
            sequential=True
        )
        self.add_strategy(fit_colonoscopy)

        # 乙状结直肠镜+结肠镜贯序筛查策略
        sigmoidoscopy_colonoscopy = ScreeningStrategy(
            name="sigmoidoscopy_colonoscopy",
            start_age=50,
            end_age=75,
            tools=[ScreeningTool.SIGMOIDOSCOPY, ScreeningTool.COLONOSCOPY],
            intervals=[5, 10],
            sequential=True
        )
        self.add_strategy(sigmoidoscopy_colonoscopy)

        # 添加季度级别的测试策略
        # 每1.5年FIT筛查策略
        fit_1_5_years = ScreeningStrategy(
            name="fit_1_5_years",
            start_age=50,
            end_age=75,
            tools=[ScreeningTool.FIT],
            intervals=[1.5],
            sequential=False
        )
        self.add_strategy(fit_1_5_years)

        # 每6个月FIT筛查策略（高频筛查）
        fit_6_months = ScreeningStrategy(
            name="fit_6_months",
            start_age=50,
            end_age=75,
            tools=[ScreeningTool.FIT],
            intervals=[0.5],
            sequential=False
        )
        self.add_strategy(fit_6_months)

    def compare_strategies(self, population: List[Individual], strategy_names: List[str],
                          years: int, start_year: int = 2020) -> Dict[str, Dict]:
        """比较不同筛查策略的效果"""
        comparison_results = {}

        for strategy_name in strategy_names:
            if strategy_name not in self.strategies:
                print(f"警告：策略 {strategy_name} 不存在，跳过")
                continue

            # 为每个策略创建独立的人群副本
            population_copy = [Individual(
                id=ind.id,
                gender=ind.gender,
                birth_year=ind.birth_year,
                current_age=ind.current_age,
                alive=ind.alive,
                cancer_stage=ind.cancer_stage,
                risk_factors=ind.risk_factors.copy(),
                risk_score=ind.risk_score
            ) for ind in population]

            # 重置筛查历史
            self.screening_history = []

            # 模拟多年筛查
            yearly_stats = []
            for year in range(years):
                current_year = start_year + year
                stats = self.screen_population(population_copy, strategy_name, current_year)
                yearly_stats.append(stats)

            # 汇总结果
            total_stats = self.get_screening_statistics()
            comparison_results[strategy_name] = {
                'total_stats': total_stats,
                'yearly_stats': yearly_stats,
                'cost_per_case_detected': (total_stats['total_cost'] / total_stats['total_detected']
                                         if total_stats['total_detected'] > 0 else 0),
                'cost_per_treatment': (total_stats['total_cost'] / total_stats['total_treatments']
                                     if total_stats['total_treatments'] > 0 else 0)
            }

        return comparison_results

    def export_screening_results(self, file_path: str, format: str = 'csv'):
        """导出筛查结果"""
        import pandas as pd

        # 准备导出数据
        data = []
        for record in self.screening_history:
            row = {
                'year': record['year'],
                'strategy': record['strategy'],
                **record['stats']
            }
            data.append(row)

        df = pd.DataFrame(data)

        if format.lower() == 'csv':
            df.to_csv(file_path, index=False, encoding='utf-8-sig')
        elif format.lower() == 'excel':
            df.to_excel(file_path, index=False)
        elif format.lower() == 'json':
            df.to_json(file_path, orient='records', force_ascii=False, indent=2)
        else:
            raise ValueError(f"不支持的导出格式: {format}")

        print(f"筛查结果已导出到: {file_path}")

    def get_screening_history(self) -> List[Dict]:
        """获取筛查历史"""
        return self.screening_history

    def reset_screening_history(self):
        """重置筛查历史"""
        self.screening_history = []